const fs = require('fs-extra')
const path = require('path')

async function packageApp() {
  console.log('📦 Starting manual app packaging...')
  
  const appDir = 'packaged-app'
  const sourceDir = process.cwd()
  
  try {
    // Clean and create app directory
    if (fs.existsSync(appDir)) {
      await fs.remove(appDir)
    }
    await fs.ensureDir(appDir)
    
    console.log('📁 Copying application files...')
    
    // Copy essential files
    await fs.copy('src', path.join(appDir, 'src'))
    await fs.copy('dist', path.join(appDir, 'dist'))
    await fs.copy('data', path.join(appDir, 'data'))
    await fs.copy('node_modules', path.join(appDir, 'node_modules'))
    await fs.copy('package.json', path.join(appDir, 'package.json'))
    
    // Copy environment file
    if (fs.existsSync('.env')) {
      await fs.copy('.env', path.join(appDir, '.env'))
    }
    
    // Create a simple start script
    const startScript = `@echo off
echo Starting Appointment System...
node src/electron/main.js
pause`
    
    await fs.writeFile(path.join(appDir, 'start.bat'), startScript)
    
    // Create a Node.js launcher
    const launcher = `const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 Starting Appointment System...')

// Start the Electron app
const electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron.cmd')
const mainPath = path.join(__dirname, 'src', 'electron', 'main.js')

const electron = spawn(electronPath, [mainPath], {
  stdio: 'inherit',
  cwd: __dirname
})

electron.on('close', (code) => {
  console.log('Application closed with code:', code)
  process.exit(code)
})

electron.on('error', (error) => {
  console.error('Failed to start application:', error)
  process.exit(1)
})`
    
    await fs.writeFile(path.join(appDir, 'start.js'), launcher)
    
    // Create package.json for the packaged app
    const packageJson = {
      "name": "appointment-system-packaged",
      "version": "1.0.0",
      "main": "start.js",
      "scripts": {
        "start": "node start.js"
      }
    }
    
    await fs.writeFile(path.join(appDir, 'package-app.json'), JSON.stringify(packageJson, null, 2))
    
    console.log('✅ App packaged successfully!')
    console.log(`📂 Packaged app location: ${path.resolve(appDir)}`)
    console.log('🚀 To run the app:')
    console.log(`   1. Navigate to: ${path.resolve(appDir)}`)
    console.log('   2. Double-click: start.bat')
    console.log('   3. Or run: node start.js')
    
  } catch (error) {
    console.error('❌ Packaging failed:', error)
    process.exit(1)
  }
}

packageApp()
