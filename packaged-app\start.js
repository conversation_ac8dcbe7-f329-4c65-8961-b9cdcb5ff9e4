const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 Starting Appointment System...')

// Start the Electron app
const electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron.cmd')
const mainPath = path.join(__dirname, 'src', 'electron', 'main.js')

const electron = spawn(electronPath, [mainPath], {
  stdio: 'inherit',
  cwd: __dirname
})

electron.on('close', (code) => {
  console.log('Application closed with code:', code)
  process.exit(code)
})

electron.on('error', (error) => {
  console.error('Failed to start application:', error)
  process.exit(1)
})