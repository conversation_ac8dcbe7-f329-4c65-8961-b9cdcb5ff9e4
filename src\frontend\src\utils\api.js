import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:3004/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Patient API functions
export const patientsAPI = {
  // Get all patients with optional search and pagination
  getAll: (params = {}) => api.get('/patients', { params }),

  // Get a specific patient by ID
  getById: (id) => api.get(`/patients/${id}`),

  // Create a new patient
  create: (patientData) => api.post('/patients', patientData),

  // Update an existing patient
  update: (id, patientData) => api.put(`/patients/${id}`, patientData),

  // Delete a patient
  delete: (id) => api.delete(`/patients/${id}`),
}

// Appointment API functions
export const appointmentsAPI = {
  // Get all appointments with optional filters
  getAll: (params = {}) => api.get('/appointments', { params }),

  // Get a specific appointment by ID
  getById: (id) => api.get(`/appointments/${id}`),

  // Create a new appointment
  create: (appointmentData) => api.post('/appointments', appointmentData),

  // Update an existing appointment
  update: (id, appointmentData) => api.put(`/appointments/${id}`, appointmentData),

  // Delete an appointment
  delete: (id) => api.delete(`/appointments/${id}`),

  // Get appointments for calendar view
  getCalendar: (year, month) => api.get(`/appointments/calendar/${year}/${month}`),
}

// Health check function
export const healthCheck = () => api.get('/health')

export default api