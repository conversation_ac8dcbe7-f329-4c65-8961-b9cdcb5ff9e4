Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\Appointment System.lnk")

' Get the current directory
currentDir = CreateObject("Scripting.FileSystemObject").GetAbsolutePathName(".")
packagedAppDir = currentDir & "\packaged-app"

oShellLink.TargetPath = packagedAppDir & "\start.bat"
oShellLink.WorkingDirectory = packagedAppDir
oShellLink.Description = "Appointment System Desktop App"
oShellLink.IconLocation = packagedAppDir & "\start.bat,0"
oShellLink.Save

WScript.Echo "Desktop shortcut created successfully!"
WScript.Echo "You can now double-click 'Appointment System' on your desktop to run the app."
