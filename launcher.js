#!/usr/bin/env node

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

console.log('🚀 Starting Appointment System...')

// Get the directory where this executable is located
const appDir = path.dirname(process.execPath)
const isPackaged = process.pkg !== undefined

let electronPath, mainPath

if (isPackaged) {
  // When packaged with pkg, look for electron in the same directory
  electronPath = path.join(appDir, 'electron.exe')
  mainPath = path.join(appDir, 'src', 'electron', 'main.js')
  
  // Check if electron exists
  if (!fs.existsSync(electronPath)) {
    console.error('❌ Electron not found at:', electronPath)
    console.log('Please ensure electron.exe is in the same directory as this launcher.')
    process.exit(1)
  }
} else {
  // Development mode
  electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron.cmd')
  mainPath = path.join(__dirname, 'src', 'electron', 'main.js')
}

console.log('📂 App directory:', appDir)
console.log('⚡ Electron path:', electronPath)
console.log('📄 Main script:', mainPath)

// Start the Electron app
const electron = spawn(electronPath, [mainPath], {
  stdio: 'inherit',
  cwd: isPackaged ? appDir : __dirname,
  env: {
    ...process.env,
    NODE_ENV: 'production'
  }
})

electron.on('close', (code) => {
  console.log('Application closed with code:', code)
  process.exit(code)
})

electron.on('error', (error) => {
  console.error('Failed to start application:', error)
  console.log('\n🔧 Troubleshooting:')
  console.log('1. Make sure Node.js is installed')
  console.log('2. Make sure all files are in the correct location')
  console.log('3. Try running from the command line for more details')
  process.exit(1)
})
