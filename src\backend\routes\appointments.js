const express = require('express');
const router = express.Router();

// Input validation helper
const validateAppointmentInput = (data) => {
    const errors = [];

    if (!data.patient_id || isNaN(parseInt(data.patient_id))) {
        errors.push('Valid patient ID is required');
    }

    if (!data.appointment_date) {
        errors.push('Appointment date is required');
    } else {
        const date = new Date(data.appointment_date);
        if (isNaN(date.getTime())) {
            errors.push('Invalid appointment date format');
        }
    }

    if (!data.appointment_time) {
        errors.push('Appointment time is required');
    } else {
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(data.appointment_time)) {
            errors.push('Invalid appointment time format (use HH:MM)');
        }
    }

    if (!data.appointment_type || data.appointment_type.trim().length === 0) {
        errors.push('Appointment type is required');
    }

    if (data.duration && (isNaN(parseInt(data.duration)) || parseInt(data.duration) <= 0)) {
        errors.push('Duration must be a positive number');
    }

    if (data.status && !['scheduled', 'completed', 'cancelled', 'no-show'].includes(data.status)) {
        errors.push('Invalid status. Must be: scheduled, completed, cancelled, or no-show');
    }

    return errors;
};

// Helper function to check for appointment conflicts
const checkAppointmentConflict = async (db, patientId, date, time, duration = 30, excludeId = null) => {
    const appointmentDateTime = new Date(`${date} ${time}`);
    const endDateTime = new Date(appointmentDateTime.getTime() + (duration * 60000));

    let sql = `
        SELECT a.*, p.first_name, p.last_name
        FROM appointments a
        JOIN patients p ON a.patient_id = p.id
        WHERE a.appointment_date = ?
        AND a.status != 'cancelled'
        AND (
            (a.appointment_time <= ? AND
             datetime(a.appointment_date || ' ' || a.appointment_time, '+' || a.duration || ' minutes') > ?)
            OR
            (? <= a.appointment_time AND ? > a.appointment_time)
        )
    `;

    let params = [date, time, `${date} ${time}`, `${date} ${time}`, endDateTime.toTimeString().slice(0, 5)];

    if (excludeId) {
        sql += ' AND a.id != ?';
        params.push(excludeId);
    }

    const conflicts = await db.all(sql, params);
    return conflicts;
};

// GET /api/appointments - Get all appointments with optional filters
router.get('/', async (req, res) => {
    try {
        const {
            patient_id,
            date,
            status,
            sortBy = 'appointment_date',
            sortOrder = 'ASC',
            limit = 100,
            offset = 0
        } = req.query;

        let sql = `
            SELECT a.*, p.first_name, p.last_name, p.phone, p.email
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
        `;
        let params = [];
        let whereConditions = [];

        // Add filters
        if (patient_id) {
            whereConditions.push('a.patient_id = ?');
            params.push(patient_id);
        }

        if (date) {
            whereConditions.push('a.appointment_date = ?');
            params.push(date);
        }

        if (status) {
            whereConditions.push('a.status = ?');
            params.push(status);
        }

        if (whereConditions.length > 0) {
            sql += ' WHERE ' + whereConditions.join(' AND ');
        }

        // Add sorting
        const validSortColumns = ['appointment_date', 'appointment_time', 'patient_id', 'status', 'created_at'];
        const validSortOrders = ['ASC', 'DESC'];

        if (validSortColumns.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
            sql += ` ORDER BY a.${sortBy} ${sortOrder.toUpperCase()}`;
        } else {
            sql += ` ORDER BY a.appointment_date ASC, a.appointment_time ASC`;
        }

        // Add pagination
        sql += ` LIMIT ? OFFSET ?`;
        params.push(parseInt(limit), parseInt(offset));

        const appointments = await req.db.all(sql, params);

        // Get total count for pagination
        let countSql = 'SELECT COUNT(*) as total FROM appointments a';
        let countParams = [];

        if (whereConditions.length > 0) {
            countSql += ' WHERE ' + whereConditions.join(' AND ');
            countParams = params.slice(0, -2); // Remove limit and offset
        }

        const countResult = await req.db.get(countSql, countParams);

        res.json({
            appointments,
            pagination: {
                total: countResult.total,
                limit: parseInt(limit),
                offset: parseInt(offset),
                hasMore: (parseInt(offset) + parseInt(limit)) < countResult.total
            }
        });
    } catch (error) {
        console.error('Error fetching appointments:', error);
        res.status(500).json({ error: 'Failed to fetch appointments' });
    }
});

// GET /api/appointments/:id - Get a specific appointment
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (!id || isNaN(parseInt(id))) {
            return res.status(400).json({ error: 'Invalid appointment ID' });
        }

        const appointment = await req.db.get(`
            SELECT a.*, p.first_name, p.last_name, p.phone, p.email, p.date_of_birth
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            WHERE a.id = ?
        `, [id]);

        if (!appointment) {
            return res.status(404).json({ error: 'Appointment not found' });
        }

        res.json(appointment);
    } catch (error) {
        console.error('Error fetching appointment:', error);
        res.status(500).json({ error: 'Failed to fetch appointment' });
    }
});

// POST /api/appointments - Create a new appointment
router.post('/', async (req, res) => {
    try {
        const appointmentData = req.body;

        // Validate input
        const validationErrors = validateAppointmentInput(appointmentData);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if patient exists
        const patient = await req.db.get('SELECT id FROM patients WHERE id = ?', [appointmentData.patient_id]);
        if (!patient) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        // Check for appointment conflicts
        const conflicts = await checkAppointmentConflict(
            req.db,
            appointmentData.patient_id,
            appointmentData.appointment_date,
            appointmentData.appointment_time,
            appointmentData.duration || 30
        );

        if (conflicts.length > 0) {
            return res.status(409).json({
                error: 'Appointment time conflict',
                details: 'This time slot overlaps with an existing appointment',
                conflicts: conflicts.map(c => ({
                    id: c.id,
                    patient: `${c.first_name} ${c.last_name}`,
                    time: `${c.appointment_time}`,
                    duration: c.duration
                }))
            });
        }

        // Insert new appointment
        const result = await req.db.run(
            `INSERT INTO appointments (patient_id, appointment_date, appointment_time, duration, appointment_type, status, notes)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
                appointmentData.patient_id,
                appointmentData.appointment_date,
                appointmentData.appointment_time,
                appointmentData.duration || 30,
                appointmentData.appointment_type.trim(),
                appointmentData.status || 'scheduled',
                appointmentData.notes ? appointmentData.notes.trim() : null
            ]
        );

        // Fetch the created appointment with patient details
        const newAppointment = await req.db.get(`
            SELECT a.*, p.first_name, p.last_name, p.phone, p.email
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            WHERE a.id = ?
        `, [result.id]);

        res.status(201).json(newAppointment);
    } catch (error) {
        console.error('Error creating appointment:', error);
        res.status(500).json({ error: 'Failed to create appointment' });
    }
});

// PUT /api/appointments/:id - Update an appointment
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const appointmentData = req.body;

        if (!id || isNaN(parseInt(id))) {
            return res.status(400).json({ error: 'Invalid appointment ID' });
        }

        // Check if appointment exists
        const existingAppointment = await req.db.get('SELECT * FROM appointments WHERE id = ?', [id]);
        if (!existingAppointment) {
            return res.status(404).json({ error: 'Appointment not found' });
        }

        // Validate input
        const validationErrors = validateAppointmentInput(appointmentData);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check if patient exists
        const patient = await req.db.get('SELECT id FROM patients WHERE id = ?', [appointmentData.patient_id]);
        if (!patient) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        // Check for appointment conflicts (excluding current appointment)
        const conflicts = await checkAppointmentConflict(
            req.db,
            appointmentData.patient_id,
            appointmentData.appointment_date,
            appointmentData.appointment_time,
            appointmentData.duration || 30,
            id
        );

        if (conflicts.length > 0) {
            return res.status(409).json({
                error: 'Appointment time conflict',
                details: 'This time slot overlaps with an existing appointment',
                conflicts: conflicts.map(c => ({
                    id: c.id,
                    patient: `${c.first_name} ${c.last_name}`,
                    time: `${c.appointment_time}`,
                    duration: c.duration
                }))
            });
        }

        // Update appointment
        await req.db.run(
            `UPDATE appointments
             SET patient_id = ?, appointment_date = ?, appointment_time = ?, duration = ?,
                 appointment_type = ?, status = ?, notes = ?
             WHERE id = ?`,
            [
                appointmentData.patient_id,
                appointmentData.appointment_date,
                appointmentData.appointment_time,
                appointmentData.duration || 30,
                appointmentData.appointment_type.trim(),
                appointmentData.status || 'scheduled',
                appointmentData.notes ? appointmentData.notes.trim() : null,
                id
            ]
        );

        // Fetch the updated appointment with patient details
        const updatedAppointment = await req.db.get(`
            SELECT a.*, p.first_name, p.last_name, p.phone, p.email
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            WHERE a.id = ?
        `, [id]);

        res.json(updatedAppointment);
    } catch (error) {
        console.error('Error updating appointment:', error);
        res.status(500).json({ error: 'Failed to update appointment' });
    }
});

// DELETE /api/appointments/:id - Delete an appointment
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (!id || isNaN(parseInt(id))) {
            return res.status(400).json({ error: 'Invalid appointment ID' });
        }

        // Check if appointment exists and get details
        const existingAppointment = await req.db.get(`
            SELECT a.*, p.first_name, p.last_name
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            WHERE a.id = ?
        `, [id]);

        if (!existingAppointment) {
            return res.status(404).json({ error: 'Appointment not found' });
        }

        // Delete appointment
        await req.db.run('DELETE FROM appointments WHERE id = ?', [id]);

        res.json({
            message: 'Appointment deleted successfully',
            deletedAppointment: existingAppointment
        });
    } catch (error) {
        console.error('Error deleting appointment:', error);
        res.status(500).json({ error: 'Failed to delete appointment' });
    }
});

// GET /api/appointments/calendar/:year/:month - Get appointments for calendar view
router.get('/calendar/:year/:month', async (req, res) => {
    try {
        const { year, month } = req.params;

        if (!year || !month || isNaN(parseInt(year)) || isNaN(parseInt(month))) {
            return res.status(400).json({ error: 'Invalid year or month' });
        }

        const startDate = `${year}-${month.padStart(2, '0')}-01`;
        const endDate = `${year}-${month.padStart(2, '0')}-31`;

        const appointments = await req.db.all(`
            SELECT a.*, p.first_name, p.last_name, p.phone
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            WHERE a.appointment_date BETWEEN ? AND ?
            ORDER BY a.appointment_date ASC, a.appointment_time ASC
        `, [startDate, endDate]);

        res.json(appointments);
    } catch (error) {
        console.error('Error fetching calendar appointments:', error);
        res.status(500).json({ error: 'Failed to fetch calendar appointments' });
    }
});

module.exports = router;