#!/usr/bin/env node

/**
 * Simple API Test Script
 * Tests the basic functionality of the Appointment System API
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

class APITester {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE,
      timeout: 5000,
    });
  }

  async runTests() {
    console.log('🧪 Starting API Tests...\n');

    try {
      await this.testHealthCheck();
      await this.testPatients();
      await this.testAppointments();
      console.log('\n✅ All tests completed successfully!');
    } catch (error) {
      console.error('\n❌ Tests failed:', error.message);
      process.exit(1);
    }
  }

  async testHealthCheck() {
    console.log('🔍 Testing health check...');
    try {
      const response = await this.api.get('/health');
      console.log('✅ Health check passed:', response.data.status);
    } catch (error) {
      throw new Error(`Health check failed: ${error.message}`);
    }
  }

  async testPatients() {
    console.log('\n👥 Testing patients endpoints...');

    // Test GET all patients
    console.log('  📋 Getting all patients...');
    const patientsResponse = await this.api.get('/patients');
    const patients = patientsResponse.data.patients;
    console.log(`  ✅ Found ${patients.length} patients`);

    if (patients.length > 0) {
      // Test GET specific patient
      const firstPatient = patients[0];
      console.log(`  🔍 Getting patient ${firstPatient.id}...`);
      const patientResponse = await this.api.get(`/patients/${firstPatient.id}`);
      console.log(`  ✅ Retrieved patient: ${patientResponse.data.first_name} ${patientResponse.data.last_name}`);

      // Test search
      console.log('  🔎 Testing patient search...');
      const searchResponse = await this.api.get('/patients?search=John');
      console.log(`  ✅ Search returned ${searchResponse.data.patients.length} results`);
    }

    // Test creating a new patient
    console.log('  ➕ Creating new test patient...');
    const newPatient = {
      first_name: 'Test',
      last_name: 'Patient',
      date_of_birth: '1990-01-01',
      phone: '(*************',
      email: '<EMAIL>',
      address: '123 Test St, Test City, TS 12345',
      medical_notes: 'Test patient for API testing'
    };

    const createResponse = await this.api.post('/patients', newPatient);
    const createdPatient = createResponse.data;
    console.log(`  ✅ Created patient with ID: ${createdPatient.id}`);

    // Test updating the patient
    console.log('  ✏️ Updating test patient...');
    const updatedData = { ...newPatient, phone: '(*************' };
    const updateResponse = await this.api.put(`/patients/${createdPatient.id}`, updatedData);
    console.log(`  ✅ Updated patient phone: ${updateResponse.data.phone}`);

    // Test deleting the patient
    console.log('  🗑️ Deleting test patient...');
    await this.api.delete(`/patients/${createdPatient.id}`);
    console.log('  ✅ Test patient deleted');
  }

  async testAppointments() {
    console.log('\n📅 Testing appointments endpoints...');

    // Get patients for appointment testing
    const patientsResponse = await this.api.get('/patients?limit=1');
    const patients = patientsResponse.data.patients;

    if (patients.length === 0) {
      console.log('  ⚠️ No patients found, skipping appointment tests');
      return;
    }

    const testPatient = patients[0];

    // Test GET all appointments
    console.log('  📋 Getting all appointments...');
    const appointmentsResponse = await this.api.get('/appointments');
    const appointments = appointmentsResponse.data.appointments;
    console.log(`  ✅ Found ${appointments.length} appointments`);

    if (appointments.length > 0) {
      // Test GET specific appointment
      const firstAppointment = appointments[0];
      console.log(`  🔍 Getting appointment ${firstAppointment.id}...`);
      const appointmentResponse = await this.api.get(`/appointments/${firstAppointment.id}`);
      console.log(`  ✅ Retrieved appointment for: ${appointmentResponse.data.first_name} ${appointmentResponse.data.last_name}`);

      // Test filtering by status
      console.log('  🔎 Testing appointment filtering...');
      const filterResponse = await this.api.get('/appointments?status=scheduled');
      console.log(`  ✅ Filter returned ${filterResponse.data.appointments.length} scheduled appointments`);
    }

    // Test creating a new appointment
    console.log('  ➕ Creating new test appointment...');
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const appointmentDate = tomorrow.toISOString().split('T')[0];

    const newAppointment = {
      patient_id: testPatient.id,
      appointment_date: appointmentDate,
      appointment_time: '14:30',
      duration: 30,
      appointment_type: 'Test Appointment',
      status: 'scheduled',
      notes: 'Test appointment for API testing'
    };

    const createResponse = await this.api.post('/appointments', newAppointment);
    const createdAppointment = createResponse.data;
    console.log(`  ✅ Created appointment with ID: ${createdAppointment.id}`);

    // Test updating the appointment
    console.log('  ✏️ Updating test appointment...');
    const updatedData = { ...newAppointment, duration: 45 };
    const updateResponse = await this.api.put(`/appointments/${createdAppointment.id}`, updatedData);
    console.log(`  ✅ Updated appointment duration: ${updateResponse.data.duration} minutes`);

    // Test calendar endpoint
    console.log('  📅 Testing calendar endpoint...');
    const year = tomorrow.getFullYear();
    const month = tomorrow.getMonth() + 1;
    const calendarResponse = await this.api.get(`/appointments/calendar/${year}/${month}`);
    console.log(`  ✅ Calendar returned ${calendarResponse.data.length} appointments for ${year}-${month}`);

    // Test deleting the appointment
    console.log('  🗑️ Deleting test appointment...');
    await this.api.delete(`/appointments/${createdAppointment.id}`);
    console.log('  ✅ Test appointment deleted');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new APITester();

  console.log('⏳ Waiting for server to be ready...');

  // Wait a bit for server to start, then run tests
  setTimeout(() => {
    tester.runTests().catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
  }, 2000);
}

module.exports = APITester;