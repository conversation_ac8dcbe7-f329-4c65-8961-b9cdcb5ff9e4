import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Users, Clock, Calendar, TrendingUp } from 'lucide-react'
import { patientsAPI, appointmentsAPI } from '../utils/api'
import { useNotification } from '../components/NotificationProvider'
import { format } from 'date-fns'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalPatients: 0,
    totalAppointments: 0,
    todayAppointments: 0,
    upcomingAppointments: 0
  })
  const [recentAppointments, setRecentAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const { showError } = useNotification()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // Get patients count
      const patientsResponse = await patientsAPI.getAll({ limit: 1 })
      const totalPatients = patientsResponse.data.pagination.total

      // Get appointments count
      const appointmentsResponse = await appointmentsAPI.getAll({ limit: 1 })
      const totalAppointments = appointmentsResponse.data.pagination.total

      // Get today's appointments
      const today = format(new Date(), 'yyyy-MM-dd')
      const todayResponse = await appointmentsAPI.getAll({
        date: today,
        status: 'scheduled'
      })
      const todayAppointments = todayResponse.data.pagination.total

      // Get upcoming appointments (next 7 days)
      const upcomingResponse = await appointmentsAPI.getAll({
        status: 'scheduled',
        limit: 50
      })
      const upcomingAppointments = upcomingResponse.data.appointments.filter(apt => {
        const aptDate = new Date(apt.appointment_date)
        const nextWeek = new Date()
        nextWeek.setDate(nextWeek.getDate() + 7)
        return aptDate >= new Date() && aptDate <= nextWeek
      }).length

      // Get recent appointments for the list
      const recentResponse = await appointmentsAPI.getAll({
        limit: 5,
        sortBy: 'created_at',
        sortOrder: 'DESC'
      })

      setStats({
        totalPatients,
        totalAppointments,
        todayAppointments,
        upcomingAppointments
      })

      setRecentAppointments(recentResponse.data.appointments)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      showError('Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'Total Patients',
      value: stats.totalPatients,
      icon: Users,
      color: 'blue',
      link: '/patients'
    },
    {
      title: 'Total Appointments',
      value: stats.totalAppointments,
      icon: Clock,
      color: 'green',
      link: '/appointments'
    },
    {
      title: 'Today\'s Appointments',
      value: stats.todayAppointments,
      icon: Calendar,
      color: 'orange',
      link: '/calendar'
    },
    {
      title: 'Upcoming (7 days)',
      value: stats.upcomingAppointments,
      icon: TrendingUp,
      color: 'purple',
      link: '/calendar'
    }
  ]

  if (loading) {
    return (
      <div className="page-container">
        <div className="page-header">
          <h1>Dashboard</h1>
        </div>
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <h1>Dashboard</h1>
        <p>Welcome to the Appointment Management System</p>
      </div>

      <div className="dashboard-stats">
        {statCards.map((card) => {
          const IconComponent = card.icon
          return (
            <Link
              key={card.title}
              to={card.link}
              className={`stat-card stat-card-${card.color}`}
            >
              <div className="stat-icon">
                <IconComponent size={24} />
              </div>
              <div className="stat-content">
                <h3>{card.value}</h3>
                <p>{card.title}</p>
              </div>
            </Link>
          )
        })}
      </div>

      <div className="dashboard-content">
        <div className="dashboard-section">
          <div className="section-header">
            <h2>Recent Appointments</h2>
            <Link to="/appointments" className="btn btn-outline">
              View All
            </Link>
          </div>

          {recentAppointments.length > 0 ? (
            <div className="appointments-list">
              {recentAppointments.map((appointment) => (
                <div key={appointment.id} className="appointment-item">
                  <div className="appointment-info">
                    <h4>{appointment.first_name} {appointment.last_name}</h4>
                    <p>{appointment.appointment_type}</p>
                  </div>
                  <div className="appointment-details">
                    <span className="appointment-date">
                      {format(new Date(appointment.appointment_date), 'MMM dd, yyyy')}
                    </span>
                    <span className="appointment-time">
                      {appointment.appointment_time}
                    </span>
                    <span className={`appointment-status status-${appointment.status}`}>
                      {appointment.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="empty-state">
              <Clock size={48} />
              <h3>No Recent Appointments</h3>
              <p>Recent appointments will appear here</p>
              <Link to="/appointments" className="btn btn-primary">
                Schedule Appointment
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard