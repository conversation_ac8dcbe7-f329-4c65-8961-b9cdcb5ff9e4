require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');

const Database = require('./database/init');
const DatabaseSeeder = require('./database/seed');
const patientsRoutes = require('./routes/patients');
const appointmentsRoutes = require('./routes/appointments');

class AppointmentServer {
    constructor() {
        this.app = express();
        this.port = process.env.PORT || 3001;
        this.database = new Database();
    }

    async initialize() {
        try {
            // Initialize database
            await this.database.initialize();

            // Seed sample data
            const seeder = new DatabaseSeeder(this.database);
            await seeder.seedSampleData();

            // Setup middleware
            this.setupMiddleware();

            // Setup routes
            this.setupRoutes();

            // Setup error handling
            this.setupErrorHandling();

            console.log('Server initialized successfully');
        } catch (error) {
            console.error('Server initialization failed:', error);
            throw error;
        }
    }

    setupMiddleware() {
        // Security middleware
        this.app.use(helmet({
            contentSecurityPolicy: false, // Disable for development
        }));

        // CORS configuration
        this.app.use(cors({
            origin: process.env.FRONTEND_URL || 'http://localhost:3000',
            credentials: true
        }));

        // Body parsing middleware
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // Request logging middleware
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
            next();
        });

        // Database middleware - attach database instance to requests
        this.app.use((req, res, next) => {
            req.db = this.database;
            next();
        });
    }

    setupRoutes() {
        // Health check endpoint
        this.app.get('/api/health', (req, res) => {
            res.json({
                status: 'OK',
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            });
        });

        // API routes
        this.app.use('/api/patients', patientsRoutes);
        this.app.use('/api/appointments', appointmentsRoutes);

        // Serve static files in production
        if (process.env.NODE_ENV === 'production') {
            this.app.use(express.static(path.join(__dirname, '../../dist')));

            // Handle React Router - send all non-API requests to index.html
            this.app.get('*', (req, res) => {
                res.sendFile(path.join(__dirname, '../../dist/index.html'));
            });
        }
    }

    setupErrorHandling() {
        // 404 handler
        this.app.use((req, res) => {
            res.status(404).json({
                error: 'Not Found',
                message: `Route ${req.method} ${req.path} not found`
            });
        });

        // Global error handler
        this.app.use((err, req, res, next) => {
            console.error('Server error:', err);

            res.status(err.status || 500).json({
                error: 'Internal Server Error',
                message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
            });
        });
    }

    async start() {
        try {
            await this.initialize();

            this.server = this.app.listen(this.port, () => {
                console.log(`Appointment System API server running on port ${this.port}`);
                console.log(`Health check: http://localhost:${this.port}/api/health`);
            });

            return this.server;
        } catch (error) {
            console.error('Failed to start server:', error);
            throw error;
        }
    }

    async stop() {
        try {
            if (this.server) {
                this.server.close();
                console.log('Server stopped');
            }

            if (this.database) {
                await this.database.close();
            }
        } catch (error) {
            console.error('Error stopping server:', error);
            throw error;
        }
    }
}

// Start server if this file is run directly
if (require.main === module) {
    const server = new AppointmentServer();

    server.start().catch(error => {
        console.error('Failed to start server:', error);
        process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\nReceived SIGINT. Gracefully shutting down...');
        await server.stop();
        process.exit(0);
    });

    process.on('SIGTERM', async () => {
        console.log('\nReceived SIGTERM. Gracefully shutting down...');
        await server.stop();
        process.exit(0);
    });
}

module.exports = AppointmentServer;