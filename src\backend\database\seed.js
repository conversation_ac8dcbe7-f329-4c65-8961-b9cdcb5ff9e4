const Database = require('./init');

class DatabaseSeeder {
    constructor(database) {
        this.db = database;
    }

    async seedSampleData() {
        try {
            console.log('Starting database seeding...');

            // Check if data already exists
            const existingPatients = await this.db.get('SELECT COUNT(*) as count FROM patients');
            if (existingPatients.count > 0) {
                console.log('Database already contains data. Skipping seeding.');
                return;
            }

            // Sample patients data
            const patients = [
                {
                    first_name: '<PERSON>',
                    last_name: '<PERSON><PERSON>',
                    date_of_birth: '1985-03-15',
                    phone: '(*************',
                    email: '<EMAIL>',
                    address: '123 Main St, Anytown, ST 12345',
                    medical_notes: 'No known allergies. Regular checkups needed.'
                },
                {
                    first_name: '<PERSON>',
                    last_name: '<PERSON>',
                    date_of_birth: '1990-07-22',
                    phone: '(*************',
                    email: '<EMAIL>',
                    address: '456 Oak Ave, Somewhere, ST 67890',
                    medical_notes: 'Allergic to penicillin. Diabetic - requires regular monitoring.'
                },
                {
                    first_name: '<PERSON>',
                    last_name: '<PERSON>',
                    date_of_birth: '1978-11-08',
                    phone: '(*************',
                    email: '<EMAIL>',
                    address: '789 Pine Rd, Elsewhere, ST 13579',
                    medical_notes: 'Hypertension. Takes daily medication.'
                },
                {
                    first_name: 'Emily',
                    last_name: 'Davis',
                    date_of_birth: '1995-02-14',
                    phone: '(*************',
                    email: '<EMAIL>',
                    address: '321 Elm St, Nowhere, ST 24680',
                    medical_notes: 'Pregnant - due date in 6 months. Regular prenatal care.'
                },
                {
                    first_name: 'Michael',
                    last_name: 'Wilson',
                    date_of_birth: '1982-09-30',
                    phone: '(*************',
                    email: '<EMAIL>',
                    address: '654 Maple Dr, Anywhere, ST 97531',
                    medical_notes: 'Recent surgery recovery. Follow-up appointments needed.'
                }
            ];

            // Insert patients
            const patientIds = [];
            for (const patient of patients) {
                const result = await this.db.run(
                    `INSERT INTO patients (first_name, last_name, date_of_birth, phone, email, address, medical_notes)
                     VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [patient.first_name, patient.last_name, patient.date_of_birth,
                     patient.phone, patient.email, patient.address, patient.medical_notes]
                );
                patientIds.push(result.id);
                console.log(`Inserted patient: ${patient.first_name} ${patient.last_name}`);
            }

            // Sample appointments data
            const appointments = [
                {
                    patient_id: patientIds[0],
                    appointment_date: '2025-07-01',
                    appointment_time: '09:00',
                    duration: 30,
                    appointment_type: 'General Checkup',
                    status: 'scheduled',
                    notes: 'Annual physical examination'
                },
                {
                    patient_id: patientIds[1],
                    appointment_date: '2025-07-01',
                    appointment_time: '10:30',
                    duration: 45,
                    appointment_type: 'Diabetes Consultation',
                    status: 'scheduled',
                    notes: 'Blood sugar monitoring and medication review'
                },
                {
                    patient_id: patientIds[2],
                    appointment_date: '2025-07-02',
                    appointment_time: '14:00',
                    duration: 30,
                    appointment_type: 'Blood Pressure Check',
                    status: 'scheduled',
                    notes: 'Routine hypertension monitoring'
                },
                {
                    patient_id: patientIds[3],
                    appointment_date: '2025-07-03',
                    appointment_time: '11:00',
                    duration: 60,
                    appointment_type: 'Prenatal Care',
                    status: 'scheduled',
                    notes: 'Monthly prenatal checkup and ultrasound'
                },
                {
                    patient_id: patientIds[4],
                    appointment_date: '2025-06-28',
                    appointment_time: '15:30',
                    duration: 30,
                    appointment_type: 'Post-Surgery Follow-up',
                    status: 'completed',
                    notes: 'Wound healing assessment - healing well'
                },
                {
                    patient_id: patientIds[0],
                    appointment_date: '2025-07-15',
                    appointment_time: '16:00',
                    duration: 30,
                    appointment_type: 'Lab Results Review',
                    status: 'scheduled',
                    notes: 'Review blood work results from physical exam'
                }
            ];

            // Insert appointments
            for (const appointment of appointments) {
                await this.db.run(
                    `INSERT INTO appointments (patient_id, appointment_date, appointment_time, duration, appointment_type, status, notes)
                     VALUES (?, ?, ?, ?, ?, ?, ?)`,
                    [appointment.patient_id, appointment.appointment_date, appointment.appointment_time,
                     appointment.duration, appointment.appointment_type, appointment.status, appointment.notes]
                );
                console.log(`Inserted appointment for patient ID ${appointment.patient_id} on ${appointment.appointment_date}`);
            }

            console.log('Database seeding completed successfully!');
            console.log(`Inserted ${patients.length} patients and ${appointments.length} appointments`);

        } catch (error) {
            console.error('Error seeding database:', error);
            throw error;
        }
    }
}

module.exports = DatabaseSeeder;