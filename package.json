{"name": "appointment-system", "version": "1.0.0", "description": "Desktop application for patient records and appointment scheduling", "main": "src/electron/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"wait-on http://localhost:3001 && npm run dev:electron\"", "dev:backend": "nodemon src/backend/server.js", "dev:frontend": "vite", "dev:electron": "electron .", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "vite build", "build:backend": "echo 'Backend build complete'", "build:electron": "electron-builder", "dist": "npm run build && npm run build:electron", "start": "electron .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "react", "express", "sqlite", "appointment", "patient", "medical"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^29.1.4", "electron-builder": "^24.13.3", "nodemon": "^3.1.0", "vite": "^5.2.0", "wait-on": "^7.2.0"}, "dependencies": {"express": "^4.19.2", "sqlite3": "^5.1.6", "cors": "^2.8.5", "helmet": "^7.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "axios": "^1.6.8", "date-fns": "^3.6.0", "react-calendar": "^4.8.0", "lucide-react": "^0.363.0"}, "build": {"appId": "com.yourcompany.appointmentsystem", "productName": "Appointment System", "directories": {"output": "dist"}, "files": ["src/electron/**/*", "src/backend/**/*", "dist/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}}}