# Appointment System

A complete desktop application for managing patient records and appointment scheduling, built with Electron, React, Express, and SQLite. This application runs entirely offline on Windows and provides a modern, user-friendly interface for healthcare providers to manage their practice.

## Features

### 🏥 Patient Management
- **Create, edit, and delete patient records**
- **Comprehensive patient information**: Name, date of birth, contact details, address, medical notes
- **Search and filter patients** by name, phone, or email
- **Patient data validation** with real-time error feedback
- **Duplicate email prevention**

### 📅 Appointment Scheduling
- **Schedule, edit, and cancel appointments**
- **Appointment details**: Patient selection, date/time, duration, type, status, notes
- **Conflict detection** to prevent double-booking
- **Multiple appointment types**: General checkup, follow-up, consultation, emergency, etc.
- **Status tracking**: Scheduled, completed, cancelled, no-show
- **Appointment filtering** by status and date

### 📊 Dashboard & Analytics
- **Overview statistics**: Total patients, appointments, today's schedule
- **Recent appointments list**
- **Quick navigation** to different sections
- **Real-time data updates**

### 🗓️ Calendar View
- **Monthly calendar** with appointment visualization
- **Day-specific appointment details**
- **Color-coded appointment status**
- **Interactive date selection**
- **Appointment density indicators**

### 💾 Data Management
- **SQLite database** for reliable offline storage
- **Automatic database initialization** with sample data
- **Data backup and restore** functionality (planned)
- **Database migrations** support

## Technical Stack

### Frontend
- **React 18+** with modern hooks and functional components
- **Vite** for fast development and building
- **React Router** for navigation
- **Axios** for API communication
- **date-fns** for date manipulation
- **Lucide React** for icons
- **Custom CSS** with CSS variables for theming

### Backend
- **Express.js** server with RESTful API
- **SQLite3** database with proper schema design
- **CORS** and security middleware
- **Input validation** and error handling
- **Structured logging**

### Desktop Framework
- **Electron** with secure configuration
- **Node.js integration** for file system access
- **Native menu system**
- **Auto-updater ready** (configurable)

### Development Tools
- **Concurrently** for running multiple processes
- **Nodemon** for backend auto-restart
- **ESLint** and **Prettier** ready
- **Hot reload** for React development

## Project Structure

```
appointment-system/
├── src/
│   ├── electron/           # Electron main process
│   │   └── main.js
│   ├── backend/            # Express server
│   │   ├── server.js
│   │   ├── database/
│   │   │   ├── init.js     # Database connection
│   │   │   ├── schema.sql  # Database schema
│   │   │   └── seed.js     # Sample data
│   │   └── routes/
│   │       ├── patients.js
│   │       └── appointments.js
│   └── frontend/           # React application
│       ├── index.html
│       └── src/
│           ├── main.jsx    # React entry point
│           ├── App.jsx     # Main app component
│           ├── components/ # Reusable components
│           ├── pages/      # Page components
│           ├── utils/      # Utilities and API
│           └── styles/     # CSS styles
├── data/                   # SQLite database files
├── dist/                   # Built frontend files
├── assets/                 # Static assets
├── package.json
├── vite.config.js
└── README.md
```

## Installation & Setup

### Prerequisites
- **Node.js 18+** (Download from [nodejs.org](https://nodejs.org/))
- **npm** (comes with Node.js)
- **Windows 10/11** (primary target platform)

### Quick Start

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd appointment-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development environment**
   ```bash
   npm run dev
   ```

   This command will:
   - Start the Express backend server on port 3001
   - Start the Vite development server on port 3000
   - Launch the Electron application
   - Initialize the SQLite database with sample data

4. **The application should open automatically**
   - If not, check the console for any error messages
   - The backend API will be available at `http://localhost:3001`
   - The frontend will be available at `http://localhost:3000`

### Alternative Development Commands

```bash
# Start only the backend server
npm run dev:backend

# Start only the frontend development server
npm run dev:frontend

# Start only the Electron app (requires backend to be running)
npm run dev:electron

# Run API tests
node test-api.js
```

## Database Schema

### Patients Table
```sql
CREATE TABLE patients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    date_of_birth DATE NOT NULL,
    phone TEXT,
    email TEXT,
    address TEXT,
    medical_notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Appointments Table
```sql
CREATE TABLE appointments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration INTEGER DEFAULT 30,
    appointment_type TEXT NOT NULL,
    status TEXT DEFAULT 'scheduled',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);
```

### Key Features
- **Foreign key constraints** ensure data integrity
- **Automatic timestamps** for audit trails
- **Indexes** for optimal query performance
- **Check constraints** for data validation
- **Triggers** for automatic timestamp updates

## API Documentation

The application provides a RESTful API for all operations. The API is automatically started when running the development environment.

### Base URL
```
http://localhost:3001/api
```

### Patients Endpoints

#### GET /api/patients
Get all patients with optional search and pagination.

**Query Parameters:**
- `search` (string): Search by name, phone, or email
- `sortBy` (string): Sort field (default: 'last_name')
- `sortOrder` (string): 'ASC' or 'DESC' (default: 'ASC')
- `limit` (number): Number of results (default: 100)
- `offset` (number): Pagination offset (default: 0)

**Response:**
```json
{
  "patients": [...],
  "pagination": {
    "total": 25,
    "limit": 100,
    "offset": 0,
    "hasMore": false
  }
}
```

#### GET /api/patients/:id
Get a specific patient by ID.

#### POST /api/patients
Create a new patient.

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "date_of_birth": "1985-03-15",
  "phone": "(*************",
  "email": "<EMAIL>",
  "address": "123 Main St, City, State 12345",
  "medical_notes": "No known allergies"
}
```

#### PUT /api/patients/:id
Update an existing patient.

#### DELETE /api/patients/:id
Delete a patient (only if no appointments exist).

### Appointments Endpoints

#### GET /api/appointments
Get all appointments with optional filters.

**Query Parameters:**
- `patient_id` (number): Filter by patient
- `date` (string): Filter by specific date (YYYY-MM-DD)
- `status` (string): Filter by status
- `sortBy` (string): Sort field
- `sortOrder` (string): 'ASC' or 'DESC'
- `limit` (number): Number of results
- `offset` (number): Pagination offset

#### GET /api/appointments/:id
Get a specific appointment by ID.

#### POST /api/appointments
Create a new appointment.

**Request Body:**
```json
{
  "patient_id": 1,
  "appointment_date": "2025-07-01",
  "appointment_time": "09:00",
  "duration": 30,
  "appointment_type": "General Checkup",
  "status": "scheduled",
  "notes": "Annual physical examination"
}
```

#### PUT /api/appointments/:id
Update an existing appointment.

#### DELETE /api/appointments/:id
Delete an appointment.

#### GET /api/appointments/calendar/:year/:month
Get appointments for calendar view.

### Health Check

#### GET /api/health
Check API server status.

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-06-27T12:00:00.000Z",
  "version": "1.0.0"
}
```

## Usage Guide

### Getting Started

1. **Launch the application** using `npm run dev`
2. **Explore the sample data** - The app comes with pre-loaded patients and appointments
3. **Navigate using the sidebar** - Dashboard, Patients, Appointments, Calendar
4. **Try creating a new patient** - Click "Add Patient" on the Patients page
5. **Schedule an appointment** - Click "Schedule Appointment" on the Appointments page

### Patient Management

#### Adding a New Patient
1. Navigate to the **Patients** page
2. Click **"Add Patient"** button
3. Fill in the required information:
   - First Name (required)
   - Last Name (required)
   - Date of Birth (required)
   - Phone, Email, Address (optional)
   - Medical Notes (optional)
4. Click **"Add Patient"** to save

#### Editing Patient Information
1. Find the patient in the list or use search
2. Click the **edit icon** (pencil) on the patient card
3. Modify the information
4. Click **"Update Patient"** to save changes

#### Searching Patients
- Use the **search box** in the page header
- Search works across name, phone, and email fields
- Results update automatically as you type

### Appointment Scheduling

#### Creating an Appointment
1. Navigate to the **Appointments** page
2. Click **"Schedule Appointment"** button
3. Select a **patient** from the dropdown
4. Choose **date and time**
5. Set **duration** (in minutes)
6. Select **appointment type**
7. Add **notes** if needed
8. Click **"Schedule Appointment"**

#### Managing Appointments
- **Edit**: Click the edit icon on any appointment
- **Delete**: Click the trash icon (with confirmation)
- **Filter**: Use the status filter to view specific appointment types
- **Status**: Update appointment status (scheduled, completed, cancelled, no-show)

### Calendar View

#### Using the Calendar
1. Navigate to the **Calendar** page
2. Use **Previous/Next** buttons to change months
3. **Click on any date** to see appointments for that day
4. **Appointment dots** show the number and status of appointments
5. **Color coding**:
   - Blue: Scheduled
   - Green: Completed
   - Red: Cancelled
   - Orange: No-show

### Dashboard Overview

The dashboard provides:
- **Quick statistics** - Total patients, appointments, today's schedule
- **Recent appointments** - Latest scheduled appointments
- **Quick navigation** - Click stat cards to jump to relevant sections

## Building for Production

### Frontend Build
```bash
npm run build:frontend
```
This creates optimized files in the `dist/` directory.

### Electron Packaging
```bash
npm run build:electron
```
This creates a distributable Electron application.

### Complete Build
```bash
npm run dist
```
This builds both frontend and Electron package.

## Testing

### Running API Tests
```bash
node test-api.js
```

This script tests:
- Health check endpoint
- Patient CRUD operations
- Appointment CRUD operations
- Search and filtering functionality
- Calendar endpoint

### Manual Testing Checklist

#### Patient Management
- [ ] Create a new patient with all fields
- [ ] Create a patient with only required fields
- [ ] Edit patient information
- [ ] Search for patients by name
- [ ] Search for patients by phone/email
- [ ] Try to create duplicate email (should fail)
- [ ] Delete a patient with no appointments

#### Appointment Management
- [ ] Schedule a new appointment
- [ ] Edit appointment details
- [ ] Change appointment status
- [ ] Try to create conflicting appointments (should fail)
- [ ] Filter appointments by status
- [ ] View appointments in calendar
- [ ] Delete an appointment

#### User Interface
- [ ] Navigate between all pages
- [ ] Responsive design on different window sizes
- [ ] Form validation messages
- [ ] Success/error notifications
- [ ] Modal dialogs open and close properly
- [ ] Search functionality works smoothly

## Troubleshooting

### Common Issues

#### "Port already in use" Error
If you see port conflicts:
```bash
# Kill processes on port 3001 (backend)
npx kill-port 3001

# Kill processes on port 3000 (frontend)
npx kill-port 3000
```

#### Database Issues
If the database fails to initialize:
1. Delete the `data/` directory
2. Restart the application with `npm run dev`
3. The database will be recreated with sample data

#### Electron Won't Start
1. Make sure both backend and frontend servers are running
2. Check console for error messages
3. Try running components separately:
   ```bash
   npm run dev:backend    # Terminal 1
   npm run dev:frontend   # Terminal 2
   npm run dev:electron   # Terminal 3
   ```

#### Frontend Not Loading
1. Ensure Vite dev server is running on port 3000
2. Check browser console for errors
3. Verify API connection to backend

#### Build Errors
1. Clear node_modules and reinstall:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```
2. Clear build cache:
   ```bash
   npm run build:frontend
   ```

### Performance Tips

1. **Database Performance**
   - The app includes indexes for common queries
   - For large datasets, consider pagination
   - Regular database maintenance (vacuum, analyze)

2. **Memory Usage**
   - Electron apps can use significant memory
   - Close unused windows/tabs
   - Restart the app periodically for long sessions

3. **Development Performance**
   - Use `npm run dev:backend` and `npm run dev:frontend` separately for faster iteration
   - Disable Electron DevTools in production builds

## Security Considerations

### Data Protection
- **Local Storage**: All data is stored locally in SQLite
- **No Network Transmission**: App works completely offline
- **Input Validation**: All user inputs are validated on both client and server
- **SQL Injection Prevention**: Parameterized queries used throughout

### Electron Security
- **Context Isolation**: Enabled for security
- **Node Integration**: Disabled in renderer process
- **Content Security Policy**: Configured for production
- **External Links**: Open in default browser, not in app

## Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Code Style
- Use modern JavaScript (ES6+)
- Follow React best practices
- Use functional components with hooks
- Maintain consistent indentation
- Add comments for complex logic

### Database Changes
- Update schema.sql for structure changes
- Create migration scripts for existing data
- Update seed.js for new sample data
- Test with both empty and populated databases

## License

This project is licensed under the MIT License. See LICENSE file for details.

## Support

For issues, questions, or contributions:
1. Check the troubleshooting section above
2. Search existing issues in the repository
3. Create a new issue with detailed information
4. Include error messages and steps to reproduce

## Acknowledgments

- **Electron** - Desktop application framework
- **React** - User interface library
- **Express** - Web application framework
- **SQLite** - Database engine
- **Vite** - Build tool and development server
- **Lucide** - Icon library

---

**Built with ❤️ for healthcare providers who need reliable, offline patient management.**