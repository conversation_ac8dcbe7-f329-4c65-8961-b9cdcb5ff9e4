const express = require('express');
const router = express.Router();

// Input validation helper
const validatePatientInput = (data) => {
    const errors = [];

    if (!data.first_name || data.first_name.trim().length === 0) {
        errors.push('First name is required');
    }

    if (!data.last_name || data.last_name.trim().length === 0) {
        errors.push('Last name is required');
    }

    if (!data.date_of_birth) {
        errors.push('Date of birth is required');
    } else {
        const dob = new Date(data.date_of_birth);
        if (isNaN(dob.getTime())) {
            errors.push('Invalid date of birth format');
        }
    }

    if (data.email && data.email.trim().length > 0) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.email)) {
            errors.push('Invalid email format');
        }
    }

    return errors;
};

// GET /api/patients - Get all patients with optional search
router.get('/', async (req, res) => {
    try {
        const { search, sortBy = 'last_name', sortOrder = 'ASC', limit = 100, offset = 0 } = req.query;

        let sql = 'SELECT * FROM patients';
        let params = [];

        // Add search functionality
        if (search && search.trim().length > 0) {
            sql += ` WHERE (first_name LIKE ? OR last_name LIKE ? OR phone LIKE ? OR email LIKE ?)`;
            const searchTerm = `%${search.trim()}%`;
            params = [searchTerm, searchTerm, searchTerm, searchTerm];
        }

        // Add sorting
        const validSortColumns = ['first_name', 'last_name', 'date_of_birth', 'created_at'];
        const validSortOrders = ['ASC', 'DESC'];

        if (validSortColumns.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
            sql += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
        } else {
            sql += ` ORDER BY last_name ASC`;
        }

        // Add pagination
        sql += ` LIMIT ? OFFSET ?`;
        params.push(parseInt(limit), parseInt(offset));

        const patients = await req.db.all(sql, params);

        // Get total count for pagination
        let countSql = 'SELECT COUNT(*) as total FROM patients';
        let countParams = [];

        if (search && search.trim().length > 0) {
            countSql += ` WHERE (first_name LIKE ? OR last_name LIKE ? OR phone LIKE ? OR email LIKE ?)`;
            const searchTerm = `%${search.trim()}%`;
            countParams = [searchTerm, searchTerm, searchTerm, searchTerm];
        }

        const countResult = await req.db.get(countSql, countParams);

        res.json({
            patients,
            pagination: {
                total: countResult.total,
                limit: parseInt(limit),
                offset: parseInt(offset),
                hasMore: (parseInt(offset) + parseInt(limit)) < countResult.total
            }
        });
    } catch (error) {
        console.error('Error fetching patients:', error);
        res.status(500).json({ error: 'Failed to fetch patients' });
    }
});

// GET /api/patients/:id - Get a specific patient
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (!id || isNaN(parseInt(id))) {
            return res.status(400).json({ error: 'Invalid patient ID' });
        }

        const patient = await req.db.get('SELECT * FROM patients WHERE id = ?', [id]);

        if (!patient) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        res.json(patient);
    } catch (error) {
        console.error('Error fetching patient:', error);
        res.status(500).json({ error: 'Failed to fetch patient' });
    }
});

// POST /api/patients - Create a new patient
router.post('/', async (req, res) => {
    try {
        const patientData = req.body;

        // Validate input
        const validationErrors = validatePatientInput(patientData);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check for duplicate email if provided
        if (patientData.email && patientData.email.trim().length > 0) {
            const existingPatient = await req.db.get(
                'SELECT id FROM patients WHERE email = ?',
                [patientData.email.trim()]
            );

            if (existingPatient) {
                return res.status(409).json({
                    error: 'A patient with this email already exists'
                });
            }
        }

        // Insert new patient
        const result = await req.db.run(
            `INSERT INTO patients (first_name, last_name, date_of_birth, phone, email, address, medical_notes)
             VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
                patientData.first_name.trim(),
                patientData.last_name.trim(),
                patientData.date_of_birth,
                patientData.phone ? patientData.phone.trim() : null,
                patientData.email ? patientData.email.trim() : null,
                patientData.address ? patientData.address.trim() : null,
                patientData.medical_notes ? patientData.medical_notes.trim() : null
            ]
        );

        // Fetch the created patient
        const newPatient = await req.db.get('SELECT * FROM patients WHERE id = ?', [result.id]);

        res.status(201).json(newPatient);
    } catch (error) {
        console.error('Error creating patient:', error);
        res.status(500).json({ error: 'Failed to create patient' });
    }
});

// PUT /api/patients/:id - Update a patient
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const patientData = req.body;

        if (!id || isNaN(parseInt(id))) {
            return res.status(400).json({ error: 'Invalid patient ID' });
        }

        // Check if patient exists
        const existingPatient = await req.db.get('SELECT * FROM patients WHERE id = ?', [id]);
        if (!existingPatient) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        // Validate input
        const validationErrors = validatePatientInput(patientData);
        if (validationErrors.length > 0) {
            return res.status(400).json({
                error: 'Validation failed',
                details: validationErrors
            });
        }

        // Check for duplicate email if provided and different from current
        if (patientData.email && patientData.email.trim().length > 0 &&
            patientData.email.trim() !== existingPatient.email) {
            const duplicatePatient = await req.db.get(
                'SELECT id FROM patients WHERE email = ? AND id != ?',
                [patientData.email.trim(), id]
            );

            if (duplicatePatient) {
                return res.status(409).json({
                    error: 'A patient with this email already exists'
                });
            }
        }

        // Update patient
        await req.db.run(
            `UPDATE patients
             SET first_name = ?, last_name = ?, date_of_birth = ?, phone = ?,
                 email = ?, address = ?, medical_notes = ?
             WHERE id = ?`,
            [
                patientData.first_name.trim(),
                patientData.last_name.trim(),
                patientData.date_of_birth,
                patientData.phone ? patientData.phone.trim() : null,
                patientData.email ? patientData.email.trim() : null,
                patientData.address ? patientData.address.trim() : null,
                patientData.medical_notes ? patientData.medical_notes.trim() : null,
                id
            ]
        );

        // Fetch the updated patient
        const updatedPatient = await req.db.get('SELECT * FROM patients WHERE id = ?', [id]);

        res.json(updatedPatient);
    } catch (error) {
        console.error('Error updating patient:', error);
        res.status(500).json({ error: 'Failed to update patient' });
    }
});

// DELETE /api/patients/:id - Delete a patient
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;

        if (!id || isNaN(parseInt(id))) {
            return res.status(400).json({ error: 'Invalid patient ID' });
        }

        // Check if patient exists
        const existingPatient = await req.db.get('SELECT * FROM patients WHERE id = ?', [id]);
        if (!existingPatient) {
            return res.status(404).json({ error: 'Patient not found' });
        }

        // Check for existing appointments
        const appointmentCount = await req.db.get(
            'SELECT COUNT(*) as count FROM appointments WHERE patient_id = ?',
            [id]
        );

        if (appointmentCount.count > 0) {
            return res.status(409).json({
                error: 'Cannot delete patient with existing appointments',
                details: `Patient has ${appointmentCount.count} appointment(s). Please cancel or delete appointments first.`
            });
        }

        // Delete patient
        await req.db.run('DELETE FROM patients WHERE id = ?', [id]);

        res.json({
            message: 'Patient deleted successfully',
            deletedPatient: existingPatient
        });
    } catch (error) {
        console.error('Error deleting patient:', error);
        res.status(500).json({ error: 'Failed to delete patient' });
    }
});

module.exports = router;