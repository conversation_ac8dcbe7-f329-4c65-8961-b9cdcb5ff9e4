const { app, BrowserWindow, Menu } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development'

// Import the server for production
let AppointmentServer
if (!isDev) {
  AppointmentServer = require('../backend/server')
}

console.log('🚀 Starting Electron app...')
console.log('Development mode:', isDev)

let mainWindow
let server

async function createWindow() {
  console.log('📱 Creating main window...')

  // Start backend server in production
  if (!isDev) {
    try {
      console.log('🔧 Starting backend server...')
      server = new AppointmentServer()
      await server.start()
      console.log('✅ Backend server started')
    } catch (error) {
      console.error('❌ Failed to start backend server:', error)
    }
  }

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
    },
    show: true,
    title: 'Appointment System',
    icon: path.join(__dirname, '../../build/icon.png')
  })

  // Load the app
  if (isDev) {
    console.log('🌐 Loading development URL: http://localhost:3002')
    mainWindow.loadURL('http://localhost:3002')
    mainWindow.webContents.openDevTools()
  } else {
    console.log('📁 Loading production files')
    const indexPath = path.join(__dirname, '../../dist/index.html')
    console.log('Loading from:', indexPath)
    mainWindow.loadFile(indexPath)
  }

  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Create application menu
  createMenu()

  console.log('✅ Window created successfully!')
}

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// App event handlers
app.whenReady().then(() => {
  console.log('⚡ App ready, creating window...')
  createWindow()
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Stop server before quitting
    if (server) {
      server.stop().then(() => {
        app.quit()
      }).catch(() => {
        app.quit()
      })
    } else {
      app.quit()
    }
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

app.on('before-quit', async () => {
  if (server) {
    try {
      await server.stop()
    } catch (error) {
      console.error('Error stopping server:', error)
    }
  }
})

console.log('📋 Electron main process loaded')