const { app, BrowserWindow } = require('electron')
const path = require('path')

console.log('🚀 Starting Electron app...')

let mainWindow

function createWindow() {
  console.log('📱 Creating main window...')

  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
    },
    show: true,
    title: 'Appointment System'
  })

  // Load the app
  console.log('🌐 Loading development URL: http://localhost:3002')
  mainWindow.loadURL('http://localhost:3002')
  mainWindow.webContents.openDevTools()

  mainWindow.on('closed', () => {
    mainWindow = null
  })

  console.log('✅ Window created successfully!')
}

// App event handlers
app.whenReady().then(() => {
  console.log('⚡ App ready, creating window...')
  createWindow()
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

console.log('📋 Electron main process loaded')

  async initialize() {
    console.log('🚀 Initializing Electron app...')

    // Set app user model ID for Windows
    if (process.platform === 'win32') {
      app.setAppUserModelId('com.yourcompany.appointmentsystem')
    }

    // Handle app events
    this.setupAppEvents()

    // Create menu
    this.createMenu()

    // Start the backend server
    await this.startServer()

    // Create the main window
    console.log('📱 Creating main window...')
    this.createMainWindow()
  }

  setupAppEvents() {
    // This method will be called when Electron has finished initialization
    app.whenReady().then(() => {
      this.initialize()
    })

    // Quit when all windows are closed, except on macOS
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.cleanup()
        app.quit()
      }
    })

    app.on('activate', () => {
      // On macOS, re-create a window when the dock icon is clicked
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createMainWindow()
      }
    })

    // Security: Prevent new window creation
    app.on('web-contents-created', (event, contents) => {
      contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault()
        shell.openExternal(navigationUrl)
      })
    })

    // Handle app termination
    app.on('before-quit', async (event) => {
      if (this.server) {
        event.preventDefault()
        await this.cleanup()
        app.quit()
      }
    })
  }

  async startServer() {
    try {
      console.log('Starting backend server...')
      this.server = new AppointmentServer()
      await this.server.start()
      console.log(`Backend server started on port ${this.serverPort}`)
    } catch (error) {
      console.error('Failed to start backend server:', error)

      // Show error dialog
      dialog.showErrorBox(
        'Server Error',
        `Failed to start the backend server: ${error.message}\n\nThe application will now exit.`
      )

      app.quit()
    }
  }

  createMainWindow() {
    console.log('🪟 Creating browser window...')

    // Create the browser window
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      icon: path.join(__dirname, '../../assets/icon.png'), // Add icon if available
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      },
      show: true, // Show immediately for debugging
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
    })

    console.log('🌐 Loading frontend...')

    // Load the frontend
    this.loadFrontend()

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      console.log('✅ Window ready to show!')
      this.mainWindow.show()
      this.mainWindow.focus()

      // Focus window
      if (isDev) {
        this.mainWindow.webContents.openDevTools()
      }
    })

    // Handle window closed
    this.mainWindow.on('closed', () => {
      console.log('🚪 Window closed')
      this.mainWindow = null
    })

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url)
      return { action: 'deny' }
    })
  }

  async loadFrontend() {
    try {
      console.log('🔄 Loading frontend...')
      console.log('Frontend URL:', this.frontendUrl)
      console.log('Development mode:', isDev)

      if (isDev) {
        // In development, wait for Vite dev server
        console.log('⏳ Waiting for dev servers...')
        await this.waitForDevServer()
        console.log('📡 Loading URL:', this.frontendUrl)
        await this.mainWindow.loadURL(this.frontendUrl)
        console.log('✅ Frontend loaded successfully!')
      } else {
        // In production, load the built files
        const filePath = path.join(__dirname, '../../dist/index.html')
        console.log('📁 Loading file:', filePath)
        await this.mainWindow.loadFile(filePath)
      }
    } catch (error) {
      console.error('❌ Failed to load frontend:', error)

      // Show error page or dialog
      dialog.showErrorBox(
        'Frontend Error',
        `Failed to load the application frontend: ${error.message}`
      )
    }
  }

  async waitForDevServer() {
    const maxAttempts = 60
    const delay = 1000

    for (let i = 0; i < maxAttempts; i++) {
      try {
        // Check if backend is running first
        const backendResponse = await fetch('http://localhost:3004/api/health')
        if (!backendResponse.ok) {
          console.log(`Waiting for backend server... (${i + 1}/${maxAttempts})`)
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }

        // Then check frontend
        const frontendResponse = await fetch(this.frontendUrl)
        if (frontendResponse.ok) {
          console.log('Both backend and frontend servers are ready')
          return
        }
      } catch (error) {
        // Servers not ready yet
      }

      console.log(`Waiting for servers... (${i + 1}/${maxAttempts})`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }

    throw new Error('Servers did not start in time. Please make sure to run "npm run dev:backend" and "npm run dev:frontend" first.')
  }

  createMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New Patient',
            accelerator: 'CmdOrCtrl+N',
            click: () => {
              // TODO: Send message to renderer to open new patient modal
              this.mainWindow.webContents.send('menu-action', 'new-patient')
            }
          },
          {
            label: 'New Appointment',
            accelerator: 'CmdOrCtrl+Shift+N',
            click: () => {
              // TODO: Send message to renderer to open new appointment modal
              this.mainWindow.webContents.send('menu-action', 'new-appointment')
            }
          },
          { type: 'separator' },
          {
            label: 'Backup Database',
            click: async () => {
              await this.backupDatabase()
            }
          },
          {
            label: 'Restore Database',
            click: async () => {
              await this.restoreDatabase()
            }
          },
          { type: 'separator' },
          {
            role: 'quit'
          }
        ]
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectall' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Navigate',
        submenu: [
          {
            label: 'Dashboard',
            accelerator: 'CmdOrCtrl+1',
            click: () => {
              this.mainWindow.webContents.send('navigate', '/')
            }
          },
          {
            label: 'Patients',
            accelerator: 'CmdOrCtrl+2',
            click: () => {
              this.mainWindow.webContents.send('navigate', '/patients')
            }
          },
          {
            label: 'Appointments',
            accelerator: 'CmdOrCtrl+3',
            click: () => {
              this.mainWindow.webContents.send('navigate', '/appointments')
            }
          },
          {
            label: 'Calendar',
            accelerator: 'CmdOrCtrl+4',
            click: () => {
              this.mainWindow.webContents.send('navigate', '/calendar')
            }
          }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      },
      {
        role: 'help',
        submenu: [
          {
            label: 'About Appointment System',
            click: () => {
              dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'About Appointment System',
                message: 'Appointment System',
                detail: 'Version 1.0.0\n\nA desktop application for managing patient records and appointment scheduling.\n\nBuilt with Electron, React, Express, and SQLite.'
              })
            }
          },
          {
            label: 'Learn More',
            click: () => {
              shell.openExternal('https://github.com/yourcompany/appointment-system')
            }
          }
        ]
      }
    ]

    // macOS specific menu adjustments
    if (process.platform === 'darwin') {
      template.unshift({
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideothers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      })

      // Window menu
      template[5].submenu = [
        { role: 'close' },
        { role: 'minimize' },
        { role: 'zoom' },
        { type: 'separator' },
        { role: 'front' }
      ]
    }

    const menu = Menu.buildFromTemplate(template)
    Menu.setApplicationMenu(menu)
  }

  async backupDatabase() {
    try {
      const { filePath } = await dialog.showSaveDialog(this.mainWindow, {
        title: 'Backup Database',
        defaultPath: `appointment-backup-${new Date().toISOString().split('T')[0]}.db`,
        filters: [
          { name: 'Database Files', extensions: ['db'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })

      if (filePath) {
        // TODO: Implement database backup
        console.log('Backup database to:', filePath)
        dialog.showMessageBox(this.mainWindow, {
          type: 'info',
          title: 'Backup Complete',
          message: 'Database backup completed successfully!'
        })
      }
    } catch (error) {
      console.error('Backup failed:', error)
      dialog.showErrorBox('Backup Failed', `Failed to backup database: ${error.message}`)
    }
  }

  async restoreDatabase() {
    try {
      const { filePaths } = await dialog.showOpenDialog(this.mainWindow, {
        title: 'Restore Database',
        filters: [
          { name: 'Database Files', extensions: ['db'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      })

      if (filePaths && filePaths.length > 0) {
        const result = await dialog.showMessageBox(this.mainWindow, {
          type: 'warning',
          title: 'Restore Database',
          message: 'This will replace your current database with the backup.',
          detail: 'All current data will be lost. Are you sure you want to continue?',
          buttons: ['Cancel', 'Restore'],
          defaultId: 0,
          cancelId: 0
        })

        if (result.response === 1) {
          // TODO: Implement database restore
          console.log('Restore database from:', filePaths[0])
          dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'Restore Complete',
            message: 'Database restore completed successfully!'
          })
        }
      }
    } catch (error) {
      console.error('Restore failed:', error)
      dialog.showErrorBox('Restore Failed', `Failed to restore database: ${error.message}`)
    }
  }

  async cleanup() {
    try {
      console.log('Cleaning up...')

      if (this.server) {
        console.log('Stopping backend server...')
        await this.server.stop()
        this.server = null
      }

      console.log('Cleanup completed')
    } catch (error) {
      console.error('Error during cleanup:', error)
    }
  }
}

// Create and start the application
const electronApp = new ElectronApp()

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error)
  dialog.showErrorBox('Unexpected Error', `An unexpected error occurred: ${error.message}`)
})

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})