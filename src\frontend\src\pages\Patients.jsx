import React, { useState, useEffect } from 'react'
import { Search, Plus, Edit, Trash2, Phone, Mail, Calendar } from 'lucide-react'
import { patientsAPI } from '../utils/api'
import { useNotification } from '../components/NotificationProvider'
import { format } from 'date-fns'

const Patients = () => {
  const [patients, setPatients] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [editingPatient, setEditingPatient] = useState(null)
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    date_of_birth: '',
    phone: '',
    email: '',
    address: '',
    medical_notes: ''
  })
  const [formErrors, setFormErrors] = useState({})
  const [submitting, setSubmitting] = useState(false)
  const { showSuccess, showError } = useNotification()

  useEffect(() => {
    loadPatients()
  }, [])

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchTerm.trim()) {
        searchPatients(searchTerm)
      } else {
        loadPatients()
      }
    }, 300)

    return () => clearTimeout(delayedSearch)
  }, [searchTerm])

  const loadPatients = async () => {
    try {
      setLoading(true)
      const response = await patientsAPI.getAll({ limit: 100 })
      setPatients(response.data.patients)
    } catch (error) {
      console.error('Error loading patients:', error)
      showError('Failed to load patients')
    } finally {
      setLoading(false)
    }
  }

  const searchPatients = async (search) => {
    try {
      setLoading(true)
      const response = await patientsAPI.getAll({ search, limit: 100 })
      setPatients(response.data.patients)
    } catch (error) {
      console.error('Error searching patients:', error)
      showError('Failed to search patients')
    } finally {
      setLoading(false)
    }
  }

  const openModal = (patient = null) => {
    if (patient) {
      setEditingPatient(patient)
      setFormData({
        first_name: patient.first_name,
        last_name: patient.last_name,
        date_of_birth: patient.date_of_birth,
        phone: patient.phone || '',
        email: patient.email || '',
        address: patient.address || '',
        medical_notes: patient.medical_notes || ''
      })
    } else {
      setEditingPatient(null)
      setFormData({
        first_name: '',
        last_name: '',
        date_of_birth: '',
        phone: '',
        email: '',
        address: '',
        medical_notes: ''
      })
    }
    setFormErrors({})
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingPatient(null)
    setFormData({
      first_name: '',
      last_name: '',
      date_of_birth: '',
      phone: '',
      email: '',
      address: '',
      medical_notes: ''
    })
    setFormErrors({})
  }

  const validateForm = () => {
    const errors = {}

    if (!formData.first_name.trim()) {
      errors.first_name = 'First name is required'
    }

    if (!formData.last_name.trim()) {
      errors.last_name = 'Last name is required'
    }

    if (!formData.date_of_birth) {
      errors.date_of_birth = 'Date of birth is required'
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Invalid email format'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setSubmitting(true)

      if (editingPatient) {
        await patientsAPI.update(editingPatient.id, formData)
        showSuccess('Patient updated successfully')
      } else {
        await patientsAPI.create(formData)
        showSuccess('Patient created successfully')
      }

      closeModal()
      loadPatients()
    } catch (error) {
      console.error('Error saving patient:', error)
      const errorMessage = error.response?.data?.details?.join(', ') ||
                          error.response?.data?.error ||
                          'Failed to save patient'
      showError(errorMessage)
    } finally {
      setSubmitting(false)
    }
  }

  const handleDelete = async (patient) => {
    if (!window.confirm(`Are you sure you want to delete ${patient.first_name} ${patient.last_name}?`)) {
      return
    }

    try {
      await patientsAPI.delete(patient.id)
      showSuccess('Patient deleted successfully')
      loadPatients()
    } catch (error) {
      console.error('Error deleting patient:', error)
      const errorMessage = error.response?.data?.details ||
                          error.response?.data?.error ||
                          'Failed to delete patient'
      showError(errorMessage)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <h1>Patients</h1>
        <div className="page-actions">
          <div className="search-box">
            <Search size={20} />
            <input
              type="text"
              placeholder="Search patients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button
            className="btn btn-primary"
            onClick={() => openModal()}
          >
            <Plus size={20} />
            Add Patient
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading patients...</p>
        </div>
      ) : (
        <div className="patients-grid">
          {patients.length > 0 ? (
            patients.map((patient) => (
              <div key={patient.id} className="patient-card">
                <div className="patient-header">
                  <h3>{patient.first_name} {patient.last_name}</h3>
                  <div className="patient-actions">
                    <button
                      className="btn-icon"
                      onClick={() => openModal(patient)}
                      title="Edit patient"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      className="btn-icon btn-danger"
                      onClick={() => handleDelete(patient)}
                      title="Delete patient"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="patient-info">
                  <div className="info-item">
                    <Calendar size={16} />
                    <span>Born: {format(new Date(patient.date_of_birth), 'MMM dd, yyyy')}</span>
                  </div>

                  {patient.phone && (
                    <div className="info-item">
                      <Phone size={16} />
                      <span>{patient.phone}</span>
                    </div>
                  )}

                  {patient.email && (
                    <div className="info-item">
                      <Mail size={16} />
                      <span>{patient.email}</span>
                    </div>
                  )}
                </div>

                {patient.medical_notes && (
                  <div className="patient-notes">
                    <strong>Notes:</strong>
                    <p>{patient.medical_notes}</p>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="empty-state">
              <Search size={48} />
              <h3>No Patients Found</h3>
              <p>
                {searchTerm
                  ? `No patients match "${searchTerm}"`
                  : 'No patients have been added yet'
                }
              </p>
              <button
                className="btn btn-primary"
                onClick={() => openModal()}
              >
                <Plus size={20} />
                Add First Patient
              </button>
            </div>
          )}
        </div>
      )}

      {/* Patient Form Modal */}
      {showModal && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{editingPatient ? 'Edit Patient' : 'Add New Patient'}</h2>
              <button className="modal-close" onClick={closeModal}>×</button>
            </div>

            <form onSubmit={handleSubmit} className="modal-body">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="first_name">First Name *</label>
                  <input
                    type="text"
                    id="first_name"
                    name="first_name"
                    value={formData.first_name}
                    onChange={handleInputChange}
                    className={formErrors.first_name ? 'error' : ''}
                    required
                  />
                  {formErrors.first_name && (
                    <span className="error-message">{formErrors.first_name}</span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="last_name">Last Name *</label>
                  <input
                    type="text"
                    id="last_name"
                    name="last_name"
                    value={formData.last_name}
                    onChange={handleInputChange}
                    className={formErrors.last_name ? 'error' : ''}
                    required
                  />
                  {formErrors.last_name && (
                    <span className="error-message">{formErrors.last_name}</span>
                  )}
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="date_of_birth">Date of Birth *</label>
                  <input
                    type="date"
                    id="date_of_birth"
                    name="date_of_birth"
                    value={formData.date_of_birth}
                    onChange={handleInputChange}
                    className={formErrors.date_of_birth ? 'error' : ''}
                    required
                  />
                  {formErrors.date_of_birth && (
                    <span className="error-message">{formErrors.date_of_birth}</span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="phone">Phone</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="(*************"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="email">Email</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={formErrors.email ? 'error' : ''}
                  placeholder="<EMAIL>"
                />
                {formErrors.email && (
                  <span className="error-message">{formErrors.email}</span>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="address">Address</label>
                <textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  rows="2"
                  placeholder="123 Main St, City, State 12345"
                />
              </div>

              <div className="form-group">
                <label htmlFor="medical_notes">Medical Notes</label>
                <textarea
                  id="medical_notes"
                  name="medical_notes"
                  value={formData.medical_notes}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Allergies, medications, medical history..."
                />
              </div>

              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={closeModal}
                  disabled={submitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={submitting}
                >
                  {submitting ? 'Saving...' : (editingPatient ? 'Update Patient' : 'Add Patient')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default Patients