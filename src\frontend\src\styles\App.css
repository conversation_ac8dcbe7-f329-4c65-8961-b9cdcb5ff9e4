/* App Layout */
.app {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: margin-left 0.3s ease-in-out;
}

.main-content.sidebar-open {
  margin-left: var(--sidebar-width);
}

.main-content.sidebar-closed {
  margin-left: 0;
}

/* Sidebar */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
  display: none;
}

@media (max-width: 768px) {
  .sidebar-overlay {
    display: block;
  }

  .main-content.sidebar-open {
    margin-left: 0;
  }
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width);
  background-color: var(--surface-color);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  z-index: 50;
  transition: transform 0.3s ease-in-out;
}

.sidebar.closed {
  transform: translateX(-100%);
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
  min-height: var(--header-height);
}

.sidebar-brand h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s ease-in-out;
}

.sidebar-toggle:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin: 0 0.5rem 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-md);
  transition: all 0.2s ease-in-out;
  font-weight: 500;
}

.nav-link:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
}

.nav-link.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.nav-icon {
  flex-shrink: 0;
}

.nav-label {
  font-size: 0.875rem;
}

.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid var(--border-color);
}

.app-version {
  text-align: center;
  color: var(--text-muted);
}

/* Page Layout */
.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  min-height: var(--header-height);
}

.page-header h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
}

.page-header p {
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

.page-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
  }

  .page-actions {
    width: 100%;
    justify-content: space-between;
  }
}

/* Search Box */
.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box svg {
  position: absolute;
  left: 0.75rem;
  color: var(--text-muted);
  pointer-events: none;
}

.search-box input {
  padding-left: 2.5rem;
  width: 300px;
}

@media (max-width: 768px) {
  .search-box input {
    width: 200px;
  }
}

/* Filter Group */
.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-select {
  min-width: 150px;
}

/* Dashboard Styles */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease-in-out;
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-lg);
  color: white;
}

.stat-card-blue .stat-icon {
  background-color: var(--primary-color);
}

.stat-card-green .stat-icon {
  background-color: var(--success-color);
}

.stat-card-orange .stat-icon {
  background-color: var(--warning-color);
}

.stat-card-purple .stat-icon {
  background-color: #8b5cf6;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.stat-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.dashboard-content {
  flex: 1;
  padding: 0 2rem 2rem;
  overflow-y: auto;
}

.dashboard-section {
  background-color: var(--surface-color);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* Appointments List */
.appointments-list {
  padding: 1.5rem;
}

.appointment-item, .appointment-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: 1rem;
  transition: all 0.2s ease-in-out;
}

.appointment-card {
  flex-direction: column;
  align-items: stretch;
}

.appointment-item:hover, .appointment-card:hover {
  box-shadow: var(--shadow-sm);
}

.appointment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.appointment-patient {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.appointment-patient h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.appointment-actions {
  display: flex;
  gap: 0.5rem;
}

.appointment-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.appointment-info p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.appointment-details {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.appointment-date, .appointment-time {
  font-weight: 500;
  color: var(--text-primary);
}

.appointment-status, .status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-scheduled, .status-blue {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.status-completed, .status-green {
  background-color: #d1fae5;
  color: #065f46;
}

.status-cancelled, .status-red {
  background-color: #fee2e2;
  color: #dc2626;
}

.status-no-show, .status-orange {
  background-color: #fed7aa;
  color: #ea580c;
}

.appointment-notes, .appointment-contact {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.appointment-notes strong, .appointment-contact strong {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.appointment-notes p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Patients Grid */
.patients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
  padding: 2rem;
}

.patient-card {
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
}

.patient-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.patient-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.patient-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.patient-actions {
  display: flex;
  gap: 0.5rem;
}

.patient-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.patient-notes {
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.patient-notes strong {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.patient-notes p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-muted);
}

.empty-state svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.empty-state p {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  max-width: 400px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background-color: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-large {
  max-width: 600px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: none;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 1.5rem;
  transition: all 0.2s ease-in-out;
}

.modal-close:hover {
  background-color: var(--background-color);
  color: var(--text-primary);
}

.modal-body {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
  .modal {
    max-width: 95vw;
    margin: 1rem;
  }

  .modal-footer {
    flex-direction: column-reverse;
  }

  .modal-footer .btn {
    width: 100%;
  }
}

/* Calendar Styles */
.calendar-container {
  display: flex;
  gap: 2rem;
  padding: 2rem;
  height: calc(100vh - var(--header-height) - 4rem);
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.calendar-nav h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  min-width: 200px;
  text-align: center;
}

.calendar-grid {
  flex: 1;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background-color: var(--background-color);
  border-bottom: 1px solid var(--border-color);
}

.calendar-day-header {
  padding: 1rem;
  text-align: center;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.calendar-body {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: repeat(6, 1fr);
}

.calendar-day {
  border-right: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
  padding: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  min-height: 100px;
}

.calendar-day:hover {
  background-color: var(--background-color);
}

.calendar-day.other-month {
  color: var(--text-muted);
  background-color: #fafafa;
}

.calendar-day.selected {
  background-color: var(--primary-light);
}

.calendar-day.today {
  background-color: var(--primary-color);
  color: white;
}

.calendar-day.today .day-number {
  font-weight: 700;
}

.day-number {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.day-appointments {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.appointment-dot {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  opacity: 0.8;
}

.appointment-more {
  font-size: 0.75rem;
  color: var(--text-muted);
  text-align: center;
  margin-top: 2px;
}

.calendar-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  gap: 1rem;
}

.day-details {
  width: 350px;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

.day-details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.day-details-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.day-appointments-list {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.day-appointment-item {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: var(--background-color);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.appointment-time {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  min-width: 80px;
}

.appointment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.appointment-patient {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.appointment-type {
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.appointment-status {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.no-appointments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--text-muted);
}

.no-appointments svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-appointments p {
  font-size: 0.875rem;
}

@media (max-width: 1024px) {
  .calendar-container {
    flex-direction: column;
    height: auto;
  }

  .day-details {
    width: 100%;
    max-height: 400px;
  }
}

@media (max-width: 768px) {
  .calendar-day {
    min-height: 60px;
    padding: 0.25rem;
  }

  .day-number {
    font-size: 0.75rem;
  }

  .appointment-dot {
    height: 2px;
  }
}

/* Notification Styles */
.notification-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-width: 400px;
}

.notification {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background-color: var(--surface-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-error {
  border-left: 4px solid var(--error-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

.notification-info {
  border-left: 4px solid var(--primary-color);
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.notification-success .notification-icon {
  color: var(--success-color);
}

.notification-error .notification-icon {
  color: var(--error-color);
}

.notification-warning .notification-icon {
  color: var(--warning-color);
}

.notification-info .notification-icon {
  color: var(--primary-color);
}

.notification-message {
  flex: 1;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
}

.notification-close {
  flex-shrink: 0;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-muted);
  padding: 0;
  border-radius: var(--radius-sm);
  transition: color 0.2s ease-in-out;
}

.notification-close:hover {
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .notification-container {
    left: 1rem;
    right: 1rem;
    max-width: none;
  }
}