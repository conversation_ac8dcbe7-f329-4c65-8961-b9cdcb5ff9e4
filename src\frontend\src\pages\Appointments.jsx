import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Clock, User, Filter } from 'lucide-react'
import { appointmentsAPI, patientsAPI } from '../utils/api'
import { useNotification } from '../components/NotificationProvider'
import { format } from 'date-fns'

const Appointments = () => {
  const [appointments, setAppointments] = useState([])
  const [patients, setPatients] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingAppointment, setEditingAppointment] = useState(null)
  const [statusFilter, setStatusFilter] = useState('all')
  const [formData, setFormData] = useState({
    patient_id: '',
    appointment_date: '',
    appointment_time: '',
    duration: 30,
    appointment_type: '',
    status: 'scheduled',
    notes: ''
  })
  const [formErrors, setFormErrors] = useState({})
  const [submitting, setSubmitting] = useState(false)
  const { showSuccess, showError } = useNotification()

  const appointmentTypes = [
    'General Checkup',
    'Follow-up',
    'Consultation',
    'Emergency',
    'Vaccination',
    'Lab Results Review',
    'Physical Therapy',
    'Dental Cleaning',
    'Eye Exam',
    'Other'
  ]

  const statusOptions = [
    { value: 'scheduled', label: 'Scheduled', color: 'blue' },
    { value: 'completed', label: 'Completed', color: 'green' },
    { value: 'cancelled', label: 'Cancelled', color: 'red' },
    { value: 'no-show', label: 'No Show', color: 'orange' }
  ]

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    loadAppointments()
  }, [statusFilter])

  const loadData = async () => {
    await Promise.all([
      loadAppointments(),
      loadPatients()
    ])
  }

  const loadAppointments = async () => {
    try {
      setLoading(true)
      const params = { limit: 100 }
      if (statusFilter !== 'all') {
        params.status = statusFilter
      }

      const response = await appointmentsAPI.getAll(params)
      setAppointments(response.data.appointments)
    } catch (error) {
      console.error('Error loading appointments:', error)
      showError('Failed to load appointments')
    } finally {
      setLoading(false)
    }
  }

  const loadPatients = async () => {
    try {
      const response = await patientsAPI.getAll({ limit: 1000 })
      setPatients(response.data.patients)
    } catch (error) {
      console.error('Error loading patients:', error)
      showError('Failed to load patients')
    }
  }

  const openModal = (appointment = null) => {
    if (appointment) {
      setEditingAppointment(appointment)
      setFormData({
        patient_id: appointment.patient_id,
        appointment_date: appointment.appointment_date,
        appointment_time: appointment.appointment_time,
        duration: appointment.duration,
        appointment_type: appointment.appointment_type,
        status: appointment.status,
        notes: appointment.notes || ''
      })
    } else {
      setEditingAppointment(null)
      setFormData({
        patient_id: '',
        appointment_date: '',
        appointment_time: '',
        duration: 30,
        appointment_type: '',
        status: 'scheduled',
        notes: ''
      })
    }
    setFormErrors({})
    setShowModal(true)
  }

  const closeModal = () => {
    setShowModal(false)
    setEditingAppointment(null)
    setFormData({
      patient_id: '',
      appointment_date: '',
      appointment_time: '',
      duration: 30,
      appointment_type: '',
      status: 'scheduled',
      notes: ''
    })
    setFormErrors({})
  }

  const validateForm = () => {
    const errors = {}

    if (!formData.patient_id) {
      errors.patient_id = 'Patient is required'
    }

    if (!formData.appointment_date) {
      errors.appointment_date = 'Date is required'
    }

    if (!formData.appointment_time) {
      errors.appointment_time = 'Time is required'
    }

    if (!formData.appointment_type.trim()) {
      errors.appointment_type = 'Appointment type is required'
    }

    if (!formData.duration || formData.duration <= 0) {
      errors.duration = 'Duration must be greater than 0'
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    try {
      setSubmitting(true)

      if (editingAppointment) {
        await appointmentsAPI.update(editingAppointment.id, formData)
        showSuccess('Appointment updated successfully')
      } else {
        await appointmentsAPI.create(formData)
        showSuccess('Appointment created successfully')
      }

      closeModal()
      loadAppointments()
    } catch (error) {
      console.error('Error saving appointment:', error)
      const errorMessage = error.response?.data?.details ||
                          error.response?.data?.error ||
                          'Failed to save appointment'
      showError(errorMessage)
    } finally {
      setSubmitting(false)
    }
  }

  const handleDelete = async (appointment) => {
    const patientName = `${appointment.first_name} ${appointment.last_name}`
    if (!window.confirm(`Are you sure you want to delete the appointment for ${patientName}?`)) {
      return
    }

    try {
      await appointmentsAPI.delete(appointment.id)
      showSuccess('Appointment deleted successfully')
      loadAppointments()
    } catch (error) {
      console.error('Error deleting appointment:', error)
      const errorMessage = error.response?.data?.error || 'Failed to delete appointment'
      showError(errorMessage)
    }
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const getStatusColor = (status) => {
    const statusOption = statusOptions.find(opt => opt.value === status)
    return statusOption ? statusOption.color : 'gray'
  }

  return (
    <div className="page-container">
      <div className="page-header">
        <h1>Appointments</h1>
        <div className="page-actions">
          <div className="filter-group">
            <Filter size={20} />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="filter-select"
            >
              <option value="all">All Status</option>
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <button
            className="btn btn-primary"
            onClick={() => openModal()}
          >
            <Plus size={20} />
            Schedule Appointment
          </button>
        </div>
      </div>

      {loading ? (
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading appointments...</p>
        </div>
      ) : (
        <div className="appointments-list">
          {appointments.length > 0 ? (
            appointments.map((appointment) => (
              <div key={appointment.id} className="appointment-card">
                <div className="appointment-header">
                  <div className="appointment-patient">
                    <User size={20} />
                    <h3>{appointment.first_name} {appointment.last_name}</h3>
                  </div>
                  <div className="appointment-actions">
                    <button
                      className="btn-icon"
                      onClick={() => openModal(appointment)}
                      title="Edit appointment"
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      className="btn-icon btn-danger"
                      onClick={() => handleDelete(appointment)}
                      title="Delete appointment"
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                <div className="appointment-details">
                  <div className="detail-item">
                    <strong>Date:</strong>
                    <span>{format(new Date(appointment.appointment_date), 'EEEE, MMM dd, yyyy')}</span>
                  </div>

                  <div className="detail-item">
                    <Clock size={16} />
                    <span>{appointment.appointment_time} ({appointment.duration} min)</span>
                  </div>

                  <div className="detail-item">
                    <strong>Type:</strong>
                    <span>{appointment.appointment_type}</span>
                  </div>

                  <div className="detail-item">
                    <strong>Status:</strong>
                    <span className={`status-badge status-${getStatusColor(appointment.status)}`}>
                      {appointment.status}
                    </span>
                  </div>
                </div>

                {appointment.notes && (
                  <div className="appointment-notes">
                    <strong>Notes:</strong>
                    <p>{appointment.notes}</p>
                  </div>
                )}

                {appointment.phone && (
                  <div className="appointment-contact">
                    <strong>Contact:</strong>
                    <span>{appointment.phone}</span>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="empty-state">
              <Clock size={48} />
              <h3>No Appointments Found</h3>
              <p>
                {statusFilter !== 'all'
                  ? `No ${statusFilter} appointments found`
                  : 'No appointments have been scheduled yet'
                }
              </p>
              <button
                className="btn btn-primary"
                onClick={() => openModal()}
              >
                <Plus size={20} />
                Schedule First Appointment
              </button>
            </div>
          )}
        </div>
      )}

      {/* Appointment Form Modal */}
      {showModal && (
        <div className="modal-overlay" onClick={closeModal}>
          <div className="modal modal-large" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2>{editingAppointment ? 'Edit Appointment' : 'Schedule New Appointment'}</h2>
              <button className="modal-close" onClick={closeModal}>×</button>
            </div>

            <form onSubmit={handleSubmit} className="modal-body">
              <div className="form-group">
                <label htmlFor="patient_id">Patient *</label>
                <select
                  id="patient_id"
                  name="patient_id"
                  value={formData.patient_id}
                  onChange={handleInputChange}
                  className={formErrors.patient_id ? 'error' : ''}
                  required
                >
                  <option value="">Select a patient</option>
                  {patients.map(patient => (
                    <option key={patient.id} value={patient.id}>
                      {patient.first_name} {patient.last_name}
                    </option>
                  ))}
                </select>
                {formErrors.patient_id && (
                  <span className="error-message">{formErrors.patient_id}</span>
                )}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="appointment_date">Date *</label>
                  <input
                    type="date"
                    id="appointment_date"
                    name="appointment_date"
                    value={formData.appointment_date}
                    onChange={handleInputChange}
                    className={formErrors.appointment_date ? 'error' : ''}
                    min={format(new Date(), 'yyyy-MM-dd')}
                    required
                  />
                  {formErrors.appointment_date && (
                    <span className="error-message">{formErrors.appointment_date}</span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="appointment_time">Time *</label>
                  <input
                    type="time"
                    id="appointment_time"
                    name="appointment_time"
                    value={formData.appointment_time}
                    onChange={handleInputChange}
                    className={formErrors.appointment_time ? 'error' : ''}
                    required
                  />
                  {formErrors.appointment_time && (
                    <span className="error-message">{formErrors.appointment_time}</span>
                  )}
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="duration">Duration (minutes) *</label>
                  <input
                    type="number"
                    id="duration"
                    name="duration"
                    value={formData.duration}
                    onChange={handleInputChange}
                    className={formErrors.duration ? 'error' : ''}
                    min="15"
                    max="240"
                    step="15"
                    required
                  />
                  {formErrors.duration && (
                    <span className="error-message">{formErrors.duration}</span>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="status">Status</label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                  >
                    {statusOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="appointment_type">Appointment Type *</label>
                <select
                  id="appointment_type"
                  name="appointment_type"
                  value={formData.appointment_type}
                  onChange={handleInputChange}
                  className={formErrors.appointment_type ? 'error' : ''}
                  required
                >
                  <option value="">Select appointment type</option>
                  {appointmentTypes.map(type => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
                {formErrors.appointment_type && (
                  <span className="error-message">{formErrors.appointment_type}</span>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="notes">Notes</label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Additional notes about the appointment..."
                />
              </div>

              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={closeModal}
                  disabled={submitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={submitting}
                >
                  {submitting ? 'Saving...' : (editingAppointment ? 'Update Appointment' : 'Schedule Appointment')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}

export default Appointments