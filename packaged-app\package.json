{"name": "appointment-system", "version": "1.0.0", "description": "Desktop application for patient records and appointment scheduling", "main": "src/electron/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"wait-on http://localhost:3004 && npm run dev:electron\"", "dev:backend": "nodemon src/backend/server.js", "dev:frontend": "vite", "dev:electron": "electron .", "build": "npm run build:frontend", "build:frontend": "vite build", "pack": "npm run build && electron-builder --dir", "dist": "npm run build && electron-builder --publish=never", "dist:win": "npm run build && electron-builder --win --publish=never", "start": "electron .", "postinstall": "electron-builder install-app-deps", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["electron", "react", "express", "sqlite", "appointment", "patient", "medical"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "electron": "^29.1.4", "electron-builder": "^24.13.3", "nodemon": "^3.1.0", "vite": "^5.2.0", "wait-on": "^7.2.0"}, "dependencies": {"axios": "^1.6.8", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.6.0", "express": "^4.19.2", "fs-extra": "^11.3.0", "helmet": "^7.1.0", "lucide-react": "^0.363.0", "react": "^18.2.0", "react-calendar": "^4.8.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "sqlite3": "^5.1.6"}, "build": {"appId": "com.appointmentsystem.app", "productName": "Appointment System", "directories": {"output": "release", "buildResources": "build"}, "files": ["src/electron/**/*", "src/backend/**/*", "dist/**/*", "node_modules/**/*", "data/**/*", "package.json"], "nodeGypRebuild": false, "buildDependenciesFromSource": false, "extraResources": [{"from": "data", "to": "data", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Appointment System"}, "portable": {"artifactName": "AppointmentSystem-Portable.exe"}}}