import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Clock, User, Plus } from 'lucide-react'
import { appointmentsAPI } from '../utils/api'
import { useNotification } from '../components/NotificationProvider'
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, isToday } from 'date-fns'

const Calendar = () => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [appointments, setAppointments] = useState([])
  const [loading, setLoading] = useState(true)
  const [selectedDate, setSelectedDate] = useState(null)
  const [dayAppointments, setDayAppointments] = useState([])
  const { showError } = useNotification()

  useEffect(() => {
    loadCalendarData()
  }, [currentDate])

  const loadCalendarData = async () => {
    try {
      setLoading(true)
      const year = currentDate.getFullYear()
      const month = currentDate.getMonth() + 1

      const response = await appointmentsAPI.getCalendar(year, month)
      setAppointments(response.data)
    } catch (error) {
      console.error('Error loading calendar data:', error)
      showError('Failed to load calendar data')
    } finally {
      setLoading(false)
    }
  }

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate)
    newDate.setMonth(currentDate.getMonth() + direction)
    setCurrentDate(newDate)
    setSelectedDate(null)
    setDayAppointments([])
  }

  const selectDate = (date) => {
    setSelectedDate(date)
    const dateStr = format(date, 'yyyy-MM-dd')
    const dayAppts = appointments.filter(apt => apt.appointment_date === dateStr)
    setDayAppointments(dayAppts.sort((a, b) => a.appointment_time.localeCompare(b.appointment_time)))
  }

  const getAppointmentsForDate = (date) => {
    const dateStr = format(date, 'yyyy-MM-dd')
    return appointments.filter(apt => apt.appointment_date === dateStr)
  }

  const getDaysInMonth = () => {
    const start = startOfMonth(currentDate)
    const end = endOfMonth(currentDate)
    return eachDayOfInterval({ start, end })
  }

  const getCalendarDays = () => {
    const daysInMonth = getDaysInMonth()
    const firstDay = startOfMonth(currentDate)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())

    const days = []
    for (let i = 0; i < 42; i++) {
      const day = new Date(startDate)
      day.setDate(startDate.getDate() + i)
      days.push(day)
    }

    return days
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled': return 'blue'
      case 'completed': return 'green'
      case 'cancelled': return 'red'
      case 'no-show': return 'orange'
      default: return 'gray'
    }
  }

  const calendarDays = getCalendarDays()

  return (
    <div className="page-container">
      <div className="page-header">
        <h1>Calendar</h1>
        <div className="calendar-nav">
          <button
            className="btn btn-outline"
            onClick={() => navigateMonth(-1)}
          >
            <ChevronLeft size={20} />
            Previous
          </button>
          <h2>{format(currentDate, 'MMMM yyyy')}</h2>
          <button
            className="btn btn-outline"
            onClick={() => navigateMonth(1)}
          >
            Next
            <ChevronRight size={20} />
          </button>
        </div>
      </div>

      <div className="calendar-container">
        <div className="calendar-grid">
          <div className="calendar-header">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="calendar-day-header">
                {day}
              </div>
            ))}
          </div>

          <div className="calendar-body">
            {loading ? (
              <div className="calendar-loading">
                <div className="spinner"></div>
                <p>Loading calendar...</p>
              </div>
            ) : (
              calendarDays.map((day, index) => {
                const dayAppointments = getAppointmentsForDate(day)
                const isCurrentMonth = isSameMonth(day, currentDate)
                const isSelected = selectedDate && isSameDay(day, selectedDate)
                const isTodayDate = isToday(day)

                return (
                  <div
                    key={index}
                    className={`calendar-day ${!isCurrentMonth ? 'other-month' : ''} ${isSelected ? 'selected' : ''} ${isTodayDate ? 'today' : ''}`}
                    onClick={() => selectDate(day)}
                  >
                    <div className="day-number">
                      {format(day, 'd')}
                    </div>

                    {dayAppointments.length > 0 && (
                      <div className="day-appointments">
                        {dayAppointments.slice(0, 3).map((apt, idx) => (
                          <div
                            key={apt.id}
                            className={`appointment-dot status-${getStatusColor(apt.status)}`}
                            title={`${apt.appointment_time} - ${apt.first_name} ${apt.last_name}`}
                          />
                        ))}
                        {dayAppointments.length > 3 && (
                          <div className="appointment-more">
                            +{dayAppointments.length - 3}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )
              })
            )}
          </div>
        </div>

        {selectedDate && (
          <div className="day-details">
            <div className="day-details-header">
              <h3>{format(selectedDate, 'EEEE, MMMM d, yyyy')}</h3>
              <button
                className="btn btn-primary btn-sm"
                onClick={() => {/* TODO: Open appointment modal */}}
              >
                <Plus size={16} />
                Add Appointment
              </button>
            </div>

            {dayAppointments.length > 0 ? (
              <div className="day-appointments-list">
                {dayAppointments.map((appointment) => (
                  <div key={appointment.id} className="day-appointment-item">
                    <div className="appointment-time">
                      <Clock size={16} />
                      {appointment.appointment_time}
                    </div>
                    <div className="appointment-info">
                      <div className="appointment-patient">
                        <User size={16} />
                        {appointment.first_name} {appointment.last_name}
                      </div>
                      <div className="appointment-type">
                        {appointment.appointment_type}
                      </div>
                      <div className={`appointment-status status-${getStatusColor(appointment.status)}`}>
                        {appointment.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-appointments">
                <Clock size={32} />
                <p>No appointments scheduled for this day</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default Calendar